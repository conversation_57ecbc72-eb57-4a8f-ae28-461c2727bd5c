#!/usr/bin/env python3
"""
测试新增的增大减小按钮功能
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'setWindow'))

from PyQt5.QtWidgets import QApplication
from w3 import (
    calculate_ratio_size,
    validate_size_bounds,
    get_screen_info,
    COMMON_RATIOS
)

def test_calculate_ratio_size():
    """测试比例尺寸计算"""
    print("=== 测试比例尺寸计算 ===")
    
    test_cases = [
        {
            "ratio": (16, 9),
            "current": (1920, 1080),
            "scale": 1.2,
            "desc": "16:9 增大20%"
        },
        {
            "ratio": (16, 9),
            "current": (1920, 1080),
            "scale": 0.8,
            "desc": "16:9 减小20%"
        },
        {
            "ratio": (4, 3),
            "current": (800, 600),
            "scale": 1.5,
            "desc": "4:3 增大50%"
        },
        {
            "ratio": (1, 1),
            "current": (500, 500),
            "scale": 0.6,
            "desc": "1:1 减小40%"
        }
    ]
    
    for case in test_cases:
        ratio = case["ratio"]
        current_w, current_h = case["current"]
        scale = case["scale"]
        desc = case["desc"]
        
        new_w, new_h = calculate_ratio_size(ratio, current_w, current_h, scale)
        
        print(f"  {desc}:")
        print(f"    原尺寸: {current_w}x{current_h}")
        print(f"    新尺寸: {new_w}x{new_h}")
        
        # 验证比例是否正确
        original_ratio = current_w / current_h
        new_ratio = new_w / new_h
        ratio_preserved = abs(original_ratio - new_ratio) < 0.01
        
        print(f"    比例保持: {'✅' if ratio_preserved else '❌'}")
        print()

def test_validate_size_bounds():
    """测试尺寸边界验证"""
    print("=== 测试尺寸边界验证 ===")
    
    screen_w, screen_h = get_screen_info()
    
    test_cases = [
        (400, 300, "正常尺寸"),
        (299, 400, "宽度过小"),
        (400, 299, "高度过小"),
        (200, 200, "完全过小"),
        (screen_w + 100, 600, "宽度超出屏幕"),
        (800, screen_h + 100, "高度超出屏幕"),
        (screen_w + 100, screen_h + 100, "完全超出屏幕"),
        (300, 300, "最小边界"),
        (screen_w, screen_h, "屏幕边界")
    ]
    
    for width, height, desc in test_cases:
        valid, msg = validate_size_bounds(width, height)
        status = "✅ 合适" if valid else "❌ 不合适"
        print(f"  {desc} ({width}x{height}): {status}")
        if not valid:
            print(f"    原因: {msg}")

def test_ratio_scaling_scenarios():
    """测试比例缩放场景"""
    print("\n=== 测试比例缩放场景 ===")
    
    # 测试从默认尺寸开始的缩放
    print("1. 从默认尺寸开始:")
    for ratio_name, ratio_value in COMMON_RATIOS.items():
        if ratio_value and ratio_name not in ["", "自定义"]:
            # 默认高度600
            base_w, base_h = calculate_ratio_size(ratio_value, None, None, 1.0)
            
            # 增大20%
            large_w, large_h = calculate_ratio_size(ratio_value, base_w, base_h, 1.2)
            
            # 减小20%
            small_w, small_h = calculate_ratio_size(ratio_value, base_w, base_h, 0.8)
            
            print(f"  {ratio_name}:")
            print(f"    基准: {base_w}x{base_h}")
            print(f"    增大: {large_w}x{large_h}")
            print(f"    减小: {small_w}x{small_h}")
            
            # 检查边界
            large_valid, _ = validate_size_bounds(large_w, large_h)
            small_valid, _ = validate_size_bounds(small_w, small_h)
            
            print(f"    增大可行: {'✅' if large_valid else '❌'}")
            print(f"    减小可行: {'✅' if small_valid else '❌'}")
            print()

def test_extreme_scaling():
    """测试极端缩放情况"""
    print("=== 测试极端缩放情况 ===")
    
    # 测试连续缩放
    ratio = (16, 9)
    current_w, current_h = 1920, 1080
    
    print("连续减小测试:")
    for i in range(10):
        new_w, new_h = calculate_ratio_size(ratio, current_w, current_h, 0.8)
        valid, msg = validate_size_bounds(new_w, new_h)
        
        print(f"  第{i+1}次: {new_w}x{new_h} {'✅' if valid else '❌'}")
        
        if not valid:
            print(f"    停止原因: {msg}")
            break
            
        current_w, current_h = new_w, new_h
    
    print("\n连续增大测试:")
    current_w, current_h = 800, 450
    
    for i in range(10):
        new_w, new_h = calculate_ratio_size(ratio, current_w, current_h, 1.2)
        valid, msg = validate_size_bounds(new_w, new_h)
        
        print(f"  第{i+1}次: {new_w}x{new_h} {'✅' if valid else '❌'}")
        
        if not valid:
            print(f"    停止原因: {msg}")
            break
            
        current_w, current_h = new_w, new_h

def main():
    print("🔧 增大减小按钮功能测试")
    print("=" * 50)
    
    # 创建QApplication实例
    app = QApplication(sys.argv)
    
    try:
        screen_w, screen_h = get_screen_info()
        print(f"当前屏幕分辨率: {screen_w}x{screen_h}")
        print(f"最小尺寸限制: 300x300")
        print(f"最大尺寸限制: {screen_w}x{screen_h}")
        print()
        
        test_calculate_ratio_size()
        test_validate_size_bounds()
        test_ratio_scaling_scenarios()
        test_extreme_scaling()
        
        print("=" * 50)
        print("✅ 所有测试完成！")
        
        print("\n💡 新功能特点:")
        print("  🔵 增大按钮：保持比例增大20%")
        print("  🟠 减小按钮：保持比例减小20%")
        print("  🔒 尺寸限制：300x300 到 屏幕尺寸")
        print("  🎯 智能启用：仅在选择比例时可用")
        print("  📱 实时反馈：操作结果显示在状态栏")
        
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
    
    sys.exit(0)

if __name__ == "__main__":
    main()
