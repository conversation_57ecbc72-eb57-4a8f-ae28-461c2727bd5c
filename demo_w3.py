#!/usr/bin/env python3
"""
演示 w3.py 窗口管理工具的新功能
"""

import sys
import os
import time
sys.path.append(os.path.join(os.path.dirname(__file__), 'setWindow'))

def main():
    print("🚀 窗口管理工具 (w3.py) 功能演示")
    print("=" * 60)
    
    print("\n📋 新增功能概览:")
    print("1. ✨ 窗口控制按钮：最大化、最小化、关闭窗口")
    print("2. 🎯 窗口居中显示：调整后的窗口自动居中")
    print("3. 🏷️  智能窗口标题：显示程序类型 + 窗口标题")
    print("4. 🎨 优化界面布局：更合理的控件排列")
    
    print("\n🔧 技术特点:")
    print("• 保持函数式编程风格")
    print("• 新增多个工具函数：")
    print("  - get_screen_center(): 计算屏幕中心位置")
    print("  - maximize_window(): 最大化窗口")
    print("  - minimize_window(): 最小化窗口")
    print("  - close_window(): 关闭窗口")
    print("  - get_window_display_title(): 智能窗口标题显示")
    
    print("\n🎛️  界面改进:")
    print("• 窗口选择区域：")
    print("  - 添加刷新按钮")
    print("  - 三个窗口控制按钮（最大化/最小化/关闭）")
    print("  - 关闭按钮有红色警告样式")
    
    print("• 尺寸设置区域：")
    print("  - 整合到一个组框中")
    print("  - 更紧凑的布局")
    print("  - 清晰的标签说明")
    
    print("• 操作按钮区域：")
    print("  - '调整尺寸'按钮有绿色高亮")
    print("  - 按钮高度增加，更易点击")
    
    print("\n🔒 安全特性:")
    print("• 关闭窗口前会弹出确认对话框")
    print("• 所有操作都有成功/失败反馈")
    print("• 错误处理更加完善")
    
    print("\n🎯 使用场景:")
    print("• 开发者测试不同分辨率界面")
    print("• 设计师查看特定尺寸效果")
    print("• 快速管理多个应用窗口")
    print("• 多显示器环境下的窗口整理")
    
    print("\n" + "=" * 60)
    print("💡 提示：运行 'python setWindow/w3.py' 启动图形界面")
    print("💡 提示：运行 'python test_w3.py' 执行功能测试")
    
    # 询问是否启动GUI
    try:
        choice = input("\n是否现在启动窗口管理工具？(y/n): ").lower().strip()
        if choice in ['y', 'yes', '是']:
            print("正在启动窗口管理工具...")
            os.system("python setWindow/w3.py")
        else:
            print("演示结束。")
    except KeyboardInterrupt:
        print("\n\n演示结束。")

if __name__ == "__main__":
    main()
