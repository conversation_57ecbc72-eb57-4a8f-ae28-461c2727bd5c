#!/usr/bin/env python3
"""
验证 w3.py 修复后的功能
"""

import sys
import os
import subprocess
import time

def test_import():
    """测试模块导入"""
    print("=== 测试模块导入 ===")
    try:
        sys.path.append(os.path.join(os.path.dirname(__file__), 'setWindow'))
        from w3 import (
            WindowResizer, get_window_list, parse_pixel_string,
            get_screen_info, check_size_validity, COMMON_RATIOS
        )
        print("✅ 所有模块导入成功")
        return True
    except Exception as e:
        print(f"❌ 模块导入失败: {e}")
        return False

def test_basic_functions():
    """测试基本函数"""
    print("\n=== 测试基本函数 ===")
    try:
        from PyQt5.QtWidgets import QApplication
        app = QApplication(sys.argv)
        
        from w3 import get_window_list, get_screen_info, check_size_validity
        
        # 测试窗口列表
        windows = get_window_list()
        print(f"✅ 获取到 {len(windows)} 个窗口")
        
        # 测试屏幕信息
        w, h = get_screen_info()
        print(f"✅ 屏幕分辨率: {w}x{h}")
        
        # 测试尺寸检查
        valid, msg = check_size_validity(1920, 1080)
        print(f"✅ 尺寸检查功能正常: {valid}")
        
        return True
    except Exception as e:
        print(f"❌ 基本函数测试失败: {e}")
        return False

def test_gui_creation():
    """测试GUI创建"""
    print("\n=== 测试GUI创建 ===")
    try:
        from PyQt5.QtWidgets import QApplication
        app = QApplication(sys.argv)
        
        from w3 import WindowResizer
        
        # 创建窗口实例
        resizer = WindowResizer()
        print("✅ WindowResizer 实例创建成功")
        
        # 检查关键组件
        if hasattr(resizer, 'status_label'):
            print("✅ status_label 存在")
        else:
            print("❌ status_label 不存在")
            return False
            
        if hasattr(resizer, 'window_combo'):
            print("✅ window_combo 存在")
        else:
            print("❌ window_combo 不存在")
            return False
            
        # 测试消息显示功能
        resizer.show_message("测试消息", "info")
        print("✅ 消息显示功能正常")
        
        return True
    except Exception as e:
        print(f"❌ GUI创建测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_startup():
    """测试程序启动"""
    print("\n=== 测试程序启动 ===")
    try:
        # 启动程序并快速关闭
        process = subprocess.Popen(
            [sys.executable, "setWindow/w3.py"],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            cwd=os.path.dirname(__file__)
        )
        
        # 等待一秒看是否有错误
        time.sleep(1)
        
        # 检查进程状态
        if process.poll() is None:
            print("✅ 程序启动成功，正在运行")
            process.terminate()
            process.wait()
            return True
        else:
            stdout, stderr = process.communicate()
            if stderr:
                print(f"❌ 程序启动失败: {stderr.decode()}")
            else:
                print("❌ 程序意外退出")
            return False
            
    except Exception as e:
        print(f"❌ 启动测试失败: {e}")
        return False

def main():
    print("🔧 w3.py 修复验证测试")
    print("=" * 40)
    
    tests = [
        ("模块导入", test_import),
        ("基本函数", test_basic_functions), 
        ("GUI创建", test_gui_creation),
        ("程序启动", test_startup)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🧪 运行测试: {test_name}")
        if test_func():
            passed += 1
            print(f"✅ {test_name} 测试通过")
        else:
            print(f"❌ {test_name} 测试失败")
    
    print("\n" + "=" * 40)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！程序修复成功！")
        print("\n💡 现在可以安全运行:")
        print("   python setWindow/w3.py")
    else:
        print("⚠️ 部分测试失败，需要进一步检查")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
