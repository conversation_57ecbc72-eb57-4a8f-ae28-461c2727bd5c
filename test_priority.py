#!/usr/bin/env python3
"""
测试 w3.py 的功能优先级和新特性
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'setWindow'))

from PyQt5.QtWidgets import QApplication
from w3 import (
    get_screen_info,
    check_size_validity,
    COMMON_RATIOS
)

def test_screen_info():
    """测试屏幕信息获取"""
    print("=== 屏幕信息测试 ===")
    width, height = get_screen_info()
    print(f"屏幕分辨率: {width} x {height}")
    return width, height

def test_size_validation():
    """测试尺寸验证功能"""
    print("\n=== 尺寸验证测试 ===")
    
    screen_w, screen_h = get_screen_info()
    
    test_cases = [
        (1920, 1080, "常见分辨率"),
        (800, 600, "小尺寸"),
        (screen_w + 100, screen_h, "宽度超出"),
        (screen_w, screen_h + 100, "高度超出"),
        (screen_w + 100, screen_h + 100, "完全超出"),
        (screen_w, screen_h, "刚好匹配屏幕")
    ]
    
    for w, h, desc in test_cases:
        is_valid, msg = check_size_validity(w, h)
        status = "✅ 合适" if is_valid else "⚠️ 超出"
        print(f"  {desc} ({w}x{h}): {status}")
        if not is_valid:
            print(f"    {msg}")

def test_ratio_calculations():
    """测试比例计算逻辑"""
    print("\n=== 比例计算测试 ===")
    
    test_heights = [600, 800, 1080]
    
    for ratio_name, ratio_value in COMMON_RATIOS.items():
        if ratio_value and ratio_name not in ["", "自定义"]:
            print(f"\n  比例 {ratio_name} ({ratio_value[0]}:{ratio_value[1]}):")
            for height in test_heights:
                width = int(height * ratio_value[0] / ratio_value[1])
                is_valid, _ = check_size_validity(width, height)
                status = "✅" if is_valid else "⚠️"
                print(f"    高度 {height}px -> 宽度 {width}px {status}")

def test_priority_logic():
    """测试优先级逻辑"""
    print("\n=== 优先级逻辑测试 ===")
    
    scenarios = [
        {
            "name": "比例优先测试",
            "ratio": "16:9",
            "pixel": "1920x1080", 
            "custom_w": "800",
            "custom_h": "600",
            "expected": "比例优先"
        },
        {
            "name": "像素优先测试（无比例）",
            "ratio": "",
            "pixel": "2560x1440",
            "custom_w": "800", 
            "custom_h": "600",
            "expected": "像素优先"
        },
        {
            "name": "自定义优先测试（无比例无像素）",
            "ratio": "",
            "pixel": "",
            "custom_w": "1366",
            "custom_h": "768", 
            "expected": "自定义优先"
        }
    ]
    
    for scenario in scenarios:
        print(f"\n  场景: {scenario['name']}")
        print(f"    比例设置: '{scenario['ratio']}'")
        print(f"    像素设置: '{scenario['pixel']}'")
        print(f"    自定义: {scenario['custom_w']} x {scenario['custom_h']}")
        print(f"    预期结果: {scenario['expected']}")
        
        # 模拟优先级判断逻辑
        if scenario['ratio'] and scenario['ratio'] not in ["", "自定义"]:
            result = "比例优先"
        elif scenario['pixel'] and 'x' in scenario['pixel']:
            result = "像素优先"
        elif scenario['custom_w'] and scenario['custom_h']:
            result = "自定义优先"
        else:
            result = "无有效输入"
            
        status = "✅ 正确" if result == scenario['expected'] else "❌ 错误"
        print(f"    实际结果: {result} {status}")

def main():
    print("🎯 窗口管理工具优先级与功能测试")
    print("=" * 50)
    
    # 创建QApplication实例
    app = QApplication(sys.argv)
    
    try:
        test_screen_info()
        test_size_validation()
        test_ratio_calculations()
        test_priority_logic()
        
        print("\n" + "=" * 50)
        print("✅ 所有测试完成！")
        
        print("\n💡 新功能特点:")
        print("  🥇 比例调整优先级最高")
        print("  🥈 像素调整优先级第二")
        print("  🥉 窗口控制优先级第三")
        print("  📱 内置消息栏替代弹窗")
        print("  ⚠️ 智能尺寸检查与警告")
        print("  🎨 3秒自动消失的状态提示")
        
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
    
    sys.exit(0)

if __name__ == "__main__":
    main()
