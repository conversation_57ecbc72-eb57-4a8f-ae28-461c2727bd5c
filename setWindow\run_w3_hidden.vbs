' ============================================================================
' Hidden Window Management Tool Launcher
' ============================================================================
' Author: Liu Lifu
' Version: 2025-01-11-16:15:00
' Purpose: Launch w3.py completely hidden (no black window)
' ============================================================================

Set ws = CreateObject("WScript.Shell")
Set fso = CreateObject("Scripting.FileSystemObject")

' Get current script directory
scriptDir = fso.GetParentFolderName(WScript.ScriptFullName)
w3Path = scriptDir & "\w3.py"

' Check if w3.py exists
If Not fso.FileExists(w3Path) Then
    MsgBox "Error: w3.py not found in " & scriptDir, vbCritical, "Window Management Tool"
    WScript.Quit 1
End If

' Launch Python script completely hidden (0 = hidden, False = don't wait)
ws.Run "python """ & w3Path & """", 0, False

' Clean up
Set ws = Nothing
Set fso = Nothing
