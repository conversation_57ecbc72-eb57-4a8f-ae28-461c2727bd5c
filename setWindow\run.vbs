' ============================================================================
' Window Management Tool VBS Launcher
' ============================================================================
' Author: Liu Lifu
' Version: 2025-01-11-15:45:00
' Purpose: Silent launcher for w3.py using VBScript (no console window)
' ============================================================================

On Error Resume Next

' Create shell object
Set ws = CreateObject("WScript.Shell")
Set fso = CreateObject("Scripting.FileSystemObject")

' Get current script directory
scriptDir = fso.GetParentFolderName(WScript.ScriptFullName)
w3Path = scriptDir & "\w3.py"

' Check if w3.py exists
If Not fso.FileExists(w3Path) Then
    MsgBox "Error: w3.py not found in " & scriptDir & vbCrLf & vbCrLf & _
           "Please ensure w3.py is in the same directory as this VBS file.", _
           vbCritical, "Window Management Tool"
    WScript.Quit 1
End If

' Try to run with python command (assumes Python is in PATH)
command = "python """ & w3Path & """"
returnCode = ws.Run(command, 0, False)

' If python command failed, try py command (Python Launcher)
If Err.Number <> 0 Then
    Err.Clear
    command = "py """ & w3Path & """"
    returnCode = ws.Run(command, 0, False)
End If

' If still failed, show error message
If Err.Number <> 0 Then
    MsgBox "Error: Could not launch Python script." & vbCrLf & vbCrLf & _
           "Please ensure Python is installed and accessible via PATH." & vbCrLf & _
           "Required: Python 3.x with PyQt5 and pygetwindow modules.", _
           vbCritical, "Window Management Tool"
    WScript.Quit 1
End If

' Clean up objects
Set ws = Nothing
Set fso = Nothing
