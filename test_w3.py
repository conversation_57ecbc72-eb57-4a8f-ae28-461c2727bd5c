#!/usr/bin/env python3
"""
测试 w3.py 窗口管理工具的功能
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'setWindow'))

from PyQt5.QtWidgets import QApplication
from w3 import (
    get_window_list,
    parse_pixel_string,
    get_screen_center,
    get_window_display_title,
    COMMON_RATIOS,
    COMMON_PIXELS
)

def test_basic_functions():
    """测试基本功能函数"""
    print("=== 测试基本功能 ===")
    
    # 测试窗口列表获取
    print("1. 测试获取窗口列表:")
    windows = get_window_list()
    print(f"   找到 {len(windows)} 个窗口")
    for i, window in enumerate(windows[:5]):  # 只显示前5个
        print(f"   {i+1}. {window}")
    
    # 测试像素字符串解析
    print("\n2. 测试像素字符串解析:")
    test_pixels = ["1920x1080", "2560x1440", "invalid", "800x600"]
    for pixel_str in test_pixels:
        w, h = parse_pixel_string(pixel_str)
        print(f"   '{pixel_str}' -> 宽度: {w}, 高度: {h}")
    
    # 测试屏幕中心计算
    print("\n3. 测试屏幕中心计算:")
    test_sizes = [(1920, 1080), (800, 600), (1366, 768)]
    for w, h in test_sizes:
        x, y = get_screen_center(w, h)
        print(f"   {w}x{h} 窗口的中心位置: ({x}, {y})")
    
    # 测试窗口标题显示
    print("\n4. 测试窗口标题显示:")
    test_titles = [
        "Google Chrome",
        "Microsoft Word - Document1",
        "Visual Studio Code",
        "Windows PowerShell",
        "Calculator",
        "Unknown Application"
    ]
    for title in test_titles:
        display_title = get_window_display_title(title)
        print(f"   '{title}' -> '{display_title}'")

def test_constants():
    """测试常量定义"""
    print("\n=== 测试常量定义 ===")
    
    print("1. 常见比例:")
    for ratio_name, ratio_value in COMMON_RATIOS.items():
        if ratio_value:
            print(f"   {ratio_name}: {ratio_value[0]}:{ratio_value[1]}")
        else:
            print(f"   {ratio_name}: {ratio_value}")
    
    print("\n2. 常见分辨率:")
    for i, pixel in enumerate(COMMON_PIXELS):
        if pixel:
            print(f"   {i}. {pixel}")
        else:
            print(f"   {i}. (空白项)")

if __name__ == "__main__":
    print("窗口管理工具 (w3.py) 功能测试")
    print("=" * 50)

    # 创建QApplication实例用于测试Qt相关功能
    app = QApplication(sys.argv)

    try:
        test_basic_functions()
        test_constants()
        print("\n" + "=" * 50)
        print("✅ 所有测试完成！")

    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

    # 不需要运行事件循环，直接退出
    sys.exit(0)
