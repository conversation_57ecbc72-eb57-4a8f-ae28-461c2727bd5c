import sys
import pygetwindow as gw
from PyQt5.QtWidgets import (
    QApplication, QWidget, QVBoxLayout, QHBoxLayout, QLabel, QComboBox,
    QLineEdit, QPushButton, QMessageBox, QGroupBox, QFormLayout, QSizePolicy,
    QDesktopWidget
)
from PyQt5.QtCore import Qt

# 常见比例
COMMON_RATIOS = {
    "": None,  # 空白项
    "16:9": (16, 9),
    "4:3": (4, 3),
    "21:9": (21, 9),
    "1:1": (1, 1),
    "3:2": (3, 2),
    "自定义": None
}

# 常见分辨率（适配27寸和13寸屏）
COMMON_PIXELS = [
    "",  # 空白项
    "1920x1080",   # 16:9, 27寸常见
    "2560x1440",   # 16:9, 27寸高分
    "3840x2160",   # 16:9, 4K
    "2880x1800",   # 16:10, 13寸高分
    "1920x1200",   # 16:10, 13寸常见
    "2736x1824",   # 3:2, Surface Book等
    "2256x1504",   # 3:2, Surface Laptop
    "1600x900",    # 16:9, 13寸低分
    "1366x768"     # 16:9, 13寸低分
]

def get_window_list():
    windows = gw.getAllTitles()
    return [w for w in windows if w.strip()]

def parse_pixel_string(pixel_str):
    try:
        w, h = pixel_str.lower().split('x')
        return int(w), int(h)
    except Exception:
        return None, None

def get_screen_center(width, height):
    """获取屏幕中心位置坐标"""
    desktop = QDesktopWidget()
    screen_rect = desktop.screenGeometry()
    x = (screen_rect.width() - width) // 2
    y = (screen_rect.height() - height) // 2
    return x, y

def set_window_size(window_title, width, height, center=True):
    """设置窗口尺寸，默认居中显示"""
    try:
        win = gw.getWindowsWithTitle(window_title)[0]
        win.restore()
        win.resizeTo(width, height)

        if center:
            x, y = get_screen_center(width, height)
            win.moveTo(x, y)

        win.activate()
        return True, f"窗口已调整为 {width}x{height} 并居中显示"
    except Exception as e:
        return False, f"调整失败: {e}"

def maximize_window(window_title):
    """最大化窗口"""
    try:
        win = gw.getWindowsWithTitle(window_title)[0]
        win.maximize()
        win.activate()
        return True, "窗口已最大化"
    except Exception as e:
        return False, f"最大化失败: {e}"

def minimize_window(window_title):
    """最小化窗口"""
    try:
        win = gw.getWindowsWithTitle(window_title)[0]
        win.minimize()
        return True, "窗口已最小化"
    except Exception as e:
        return False, f"最小化失败: {e}"

def close_window(window_title):
    """关闭窗口"""
    try:
        win = gw.getWindowsWithTitle(window_title)[0]
        win.close()
        return True, "窗口已关闭"
    except Exception as e:
        return False, f"关闭失败: {e}"

def get_window_display_title(window_title):
    """获取窗口显示标题，格式：程序名 - 窗口标题"""
    if not window_title:
        return ""

    # 常见程序名映射
    program_mapping = {
        "chrome": "Chrome浏览器",
        "firefox": "Firefox浏览器",
        "notepad": "记事本",
        "code": "VS Code",
        "pycharm": "PyCharm",
        "word": "Microsoft Word",
        "excel": "Microsoft Excel",
        "powerpoint": "PowerPoint",
        "explorer": "文件资源管理器",
        "calculator": "计算器",
        "cmd": "命令提示符",
        "powershell": "PowerShell"
    }

    # 尝试识别程序类型
    title_lower = window_title.lower()
    program_name = "应用程序"

    for key, value in program_mapping.items():
        if key in title_lower:
            program_name = value
            break

    return f"{program_name} - {window_title}"

class WindowResizer(QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("窗口管理工具 - 尺寸调整与窗口控制")
        self.setMinimumWidth(480)
        self.setMinimumHeight(600)
        self.init_ui()

    def init_ui(self):
        main_layout = QVBoxLayout()
        main_layout.setSpacing(15)

        # 1. 应用程序窗口选择
        win_group = QGroupBox("选择目标窗口")
        win_layout = QVBoxLayout()

        # 窗口选择行
        select_layout = QHBoxLayout()
        self.window_combo = QComboBox()
        self.window_combo.setMinimumHeight(30)
        self.refresh_window_list()
        refresh_btn = QPushButton("刷新列表")
        refresh_btn.setMaximumWidth(80)
        refresh_btn.clicked.connect(self.refresh_window_list)

        select_layout.addWidget(QLabel("窗口："))
        select_layout.addWidget(self.window_combo)
        select_layout.addWidget(refresh_btn)

        # 窗口控制按钮行
        control_layout = QHBoxLayout()
        self.maximize_btn = QPushButton("最大化")
        self.minimize_btn = QPushButton("最小化")
        self.close_btn = QPushButton("关闭窗口")

        self.maximize_btn.clicked.connect(self.on_maximize_clicked)
        self.minimize_btn.clicked.connect(self.on_minimize_clicked)
        self.close_btn.clicked.connect(self.on_close_clicked)

        # 设置按钮样式
        for btn in [self.maximize_btn, self.minimize_btn, self.close_btn]:
            btn.setMinimumHeight(35)
            btn.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        self.close_btn.setStyleSheet("QPushButton { background-color: #ff6b6b; color: white; }")

        control_layout.addWidget(self.maximize_btn)
        control_layout.addWidget(self.minimize_btn)
        control_layout.addWidget(self.close_btn)

        win_layout.addLayout(select_layout)
        win_layout.addLayout(control_layout)
        win_group.setLayout(win_layout)
        main_layout.addWidget(win_group)

        # 2. 尺寸设置区域
        size_group = QGroupBox("窗口尺寸设置")
        size_layout = QVBoxLayout()

        # 常见分辨率
        pixel_layout = QHBoxLayout()
        self.pixel_combo = QComboBox()
        self.pixel_combo.addItems(COMMON_PIXELS)
        self.pixel_combo.setCurrentIndex(0)
        self.pixel_combo.currentIndexChanged.connect(self.on_pixel_combo_changed)
        pixel_layout.addWidget(QLabel("预设分辨率："))
        pixel_layout.addWidget(self.pixel_combo)
        size_layout.addLayout(pixel_layout)

        # 自定义像素输入
        custom_layout = QHBoxLayout()
        self.width_input = QLineEdit()
        self.width_input.setPlaceholderText("宽度(px)")
        self.width_input.setMinimumHeight(30)
        self.height_input = QLineEdit()
        self.height_input.setPlaceholderText("高度(px)")
        self.height_input.setMinimumHeight(30)
        custom_layout.addWidget(QLabel("自定义尺寸："))
        custom_layout.addWidget(self.width_input)
        custom_layout.addWidget(QLabel("×"))
        custom_layout.addWidget(self.height_input)
        size_layout.addLayout(custom_layout)

        # 常见比例
        ratio_layout = QHBoxLayout()
        self.ratio_combo = QComboBox()
        for k in COMMON_RATIOS.keys():
            self.ratio_combo.addItem(k)
        self.ratio_combo.setCurrentIndex(0)
        ratio_layout.addWidget(QLabel("按比例设置："))
        ratio_layout.addWidget(self.ratio_combo)
        size_layout.addLayout(ratio_layout)

        size_group.setLayout(size_layout)
        main_layout.addWidget(size_group)

        # 3. 操作按钮区
        action_group = QGroupBox("执行操作")
        action_layout = QHBoxLayout()

        self.set_btn = QPushButton("调整尺寸")
        self.set_btn.setMinimumHeight(40)
        self.set_btn.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
        self.set_btn.setStyleSheet("QPushButton { background-color: #4CAF50; color: white; font-weight: bold; }")
        self.set_btn.clicked.connect(self.on_set_clicked)

        self.reset_btn = QPushButton("重置设置")
        self.reset_btn.setMinimumHeight(40)
        self.reset_btn.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
        self.reset_btn.clicked.connect(self.on_reset_clicked)

        action_layout.addWidget(self.set_btn)
        action_layout.addWidget(self.reset_btn)
        action_group.setLayout(action_layout)
        main_layout.addWidget(action_group)

        # 4. 初始化控件状态
        self.on_reset_clicked()

        self.setLayout(main_layout)

    def refresh_window_list(self):
        """刷新窗口列表"""
        self.window_combo.clear()
        windows = get_window_list()
        # 为每个窗口添加程序类型前缀
        display_windows = [get_window_display_title(w) for w in windows]
        self.window_combo.addItems(display_windows)
        # 存储原始窗口标题用于操作
        self.original_windows = windows

    def on_pixel_combo_changed(self):
        pixel_str = self.pixel_combo.currentText()
        if pixel_str and 'x' in pixel_str:
            w, h = parse_pixel_string(pixel_str)
            if w and h:
                self.width_input.setText(str(w))
                self.height_input.setText(str(h))
                self.ratio_combo.setCurrentIndex(0)  # 清空比例选择
        else:
            self.width_input.clear()
            self.height_input.clear()

    def get_selected_window_title(self):
        """获取当前选中的原始窗口标题"""
        current_index = self.window_combo.currentIndex()
        if current_index >= 0 and hasattr(self, 'original_windows') and current_index < len(self.original_windows):
            return self.original_windows[current_index]
        return ""

    def on_maximize_clicked(self):
        """最大化窗口"""
        window_title = self.get_selected_window_title()
        if not window_title:
            QMessageBox.warning(self, "错误", "请选择一个窗口")
            return

        ok, msg = maximize_window(window_title)
        if ok:
            QMessageBox.information(self, "成功", msg)
        else:
            QMessageBox.warning(self, "错误", msg)

    def on_minimize_clicked(self):
        """最小化窗口"""
        window_title = self.get_selected_window_title()
        if not window_title:
            QMessageBox.warning(self, "错误", "请选择一个窗口")
            return

        ok, msg = minimize_window(window_title)
        if ok:
            QMessageBox.information(self, "成功", msg)
        else:
            QMessageBox.warning(self, "错误", msg)

    def on_close_clicked(self):
        """关闭窗口"""
        window_title = self.get_selected_window_title()
        if not window_title:
            QMessageBox.warning(self, "错误", "请选择一个窗口")
            return

        # 确认对话框
        reply = QMessageBox.question(self, "确认关闭",
                                   f"确定要关闭窗口 '{window_title}' 吗？",
                                   QMessageBox.Yes | QMessageBox.No,
                                   QMessageBox.No)

        if reply == QMessageBox.Yes:
            ok, msg = close_window(window_title)
            if ok:
                QMessageBox.information(self, "成功", msg)
                # 刷新窗口列表
                self.refresh_window_list()
            else:
                QMessageBox.warning(self, "错误", msg)

    def on_set_clicked(self):
        """调整窗口尺寸"""
        window_title = self.get_selected_window_title()
        if not window_title:
            QMessageBox.warning(self, "错误", "请选择一个窗口")
            return

        # 优先按常见像素
        pixel_str = self.pixel_combo.currentText()
        if pixel_str and 'x' in pixel_str:
            w, h = parse_pixel_string(pixel_str)
        # 其次按常见比例
        elif self.ratio_combo.currentText() and self.ratio_combo.currentText() != "自定义":
            ratio_key = self.ratio_combo.currentText()
            ratio = COMMON_RATIOS[ratio_key]
            try:
                h = int(self.height_input.text()) if self.height_input.text() else 600
                w = int(h * ratio[0] / ratio[1])
            except Exception:
                QMessageBox.warning(self, "错误", "请输入有效的高度或选择常见像素")
                return
        # 最后按自定义像素
        else:
            try:
                w = int(self.width_input.text())
                h = int(self.height_input.text())
            except Exception:
                QMessageBox.warning(self, "错误", "请输入有效的宽度和高度")
                return

        ok, msg = set_window_size(window_title, w, h, center=True)
        if ok:
            QMessageBox.information(self, "成功", msg)
        else:
            QMessageBox.warning(self, "错误", msg)

    def on_reset_clicked(self):
        self.pixel_combo.setCurrentIndex(0)
        self.width_input.clear()
        self.height_input.clear()
        self.ratio_combo.setCurrentIndex(0)

if __name__ == "__main__":
    app = QApplication(sys.argv)
    resizer = WindowResizer()
    resizer.show()
    sys.exit(app.exec_())
