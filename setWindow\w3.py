import sys
import pygetwindow as gw
from PyQt5.QtWidgets import (
    QApplication, QWidget, QVBoxLayout, QHBoxLayout, QLabel, QComboBox,
    QLineEdit, QPushButton, QMessageBox, QGroupBox, QFormLayout, QSizePolicy
)
from PyQt5.QtCore import Qt

# 常见比例
COMMON_RATIOS = {
    "": None,  # 空白项
    "16:9": (16, 9),
    "4:3": (4, 3),
    "21:9": (21, 9),
    "1:1": (1, 1),
    "3:2": (3, 2),
    "自定义": None
}

# 常见分辨率（适配27寸和13寸屏）
COMMON_PIXELS = [
    "",  # 空白项
    "1920x1080",   # 16:9, 27寸常见
    "2560x1440",   # 16:9, 27寸高分
    "3840x2160",   # 16:9, 4K
    "2880x1800",   # 16:10, 13寸高分
    "1920x1200",   # 16:10, 13寸常见
    "2736x1824",   # 3:2, Surface Book等
    "2256x1504",   # 3:2, Surface Laptop
    "1600x900",    # 16:9, 13寸低分
    "1366x768"     # 16:9, 13寸低分
]

def get_window_list():
    windows = gw.getAllTitles()
    return [w for w in windows if w.strip()]

def parse_pixel_string(pixel_str):
    try:
        w, h = pixel_str.lower().split('x')
        return int(w), int(h)
    except Exception:
        return None, None

def set_window_size(window_title, width, height):
    try:
        win = gw.getWindowsWithTitle(window_title)[0]
        win.restore()
        win.resizeTo(width, height)
        win.activate()
        return True, f"窗口已调整为 {width}x{height}"
    except Exception as e:
        return False, f"调整失败: {e}"

class WindowResizer(QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("应用程序窗口尺寸调整工具")
        self.setMinimumWidth(420)
        self.init_ui()

    def init_ui(self):
        main_layout = QVBoxLayout()
        main_layout.setSpacing(12)

        # 1. 应用程序窗口选择
        win_group = QGroupBox("选择窗口")
        win_layout = QHBoxLayout()
        self.window_combo = QComboBox()
        self.refresh_window_list()
        win_layout.addWidget(QLabel("窗口标题："))
        win_layout.addWidget(self.window_combo)
        win_group.setLayout(win_layout)
        main_layout.addWidget(win_group)

        # 2. 常见像素下拉框
        pixel_group = QGroupBox("常见分辨率")
        pixel_layout = QHBoxLayout()
        self.pixel_combo = QComboBox()
        self.pixel_combo.addItems(COMMON_PIXELS)
        self.pixel_combo.setCurrentIndex(0)
        self.pixel_combo.currentIndexChanged.connect(self.on_pixel_combo_changed)
        pixel_layout.addWidget(QLabel("分辨率："))
        pixel_layout.addWidget(self.pixel_combo)
        pixel_group.setLayout(pixel_layout)
        main_layout.addWidget(pixel_group)

        # 3. 自定义像素输入
        custom_group = QGroupBox("自定义像素")
        form_layout = QFormLayout()
        self.width_input = QLineEdit()
        self.width_input.setPlaceholderText("宽度(px)")
        self.height_input = QLineEdit()
        self.height_input.setPlaceholderText("高度(px)")
        form_layout.addRow("宽度：", self.width_input)
        form_layout.addRow("高度：", self.height_input)
        custom_group.setLayout(form_layout)
        main_layout.addWidget(custom_group)

        # 4. 常见比例下拉框
        ratio_group = QGroupBox("常见比例")
        ratio_layout = QHBoxLayout()
        self.ratio_combo = QComboBox()
        for k in COMMON_RATIOS.keys():
            self.ratio_combo.addItem(k)
        self.ratio_combo.setCurrentIndex(0)
        ratio_layout.addWidget(QLabel("比例："))
        ratio_layout.addWidget(self.ratio_combo)
        ratio_group.setLayout(ratio_layout)
        main_layout.addWidget(ratio_group)

        # 5. 按钮区
        btn_layout = QHBoxLayout()
        self.set_btn = QPushButton("设定")
        self.set_btn.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
        self.set_btn.clicked.connect(self.on_set_clicked)
        self.reset_btn = QPushButton("重置")
        self.reset_btn.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
        self.reset_btn.clicked.connect(self.on_reset_clicked)
        btn_layout.addWidget(self.set_btn)
        btn_layout.addWidget(self.reset_btn)
        main_layout.addLayout(btn_layout)

        # 6. 初始化控件状态
        self.on_reset_clicked()

        self.setLayout(main_layout)

    def refresh_window_list(self):
        self.window_combo.clear()
        windows = get_window_list()
        self.window_combo.addItems(windows)

    def on_pixel_combo_changed(self):
        pixel_str = self.pixel_combo.currentText()
        if pixel_str and 'x' in pixel_str:
            w, h = parse_pixel_string(pixel_str)
            if w and h:
                self.width_input.setText(str(w))
                self.height_input.setText(str(h))
                self.ratio_combo.setCurrentIndex(0)  # 清空比例选择
        else:
            self.width_input.clear()
            self.height_input.clear()

    def on_set_clicked(self):
        window_title = self.window_combo.currentText()
        if not window_title:
            QMessageBox.warning(self, "错误", "请选择一个窗口")
            return

        # 优先按常见像素
        pixel_str = self.pixel_combo.currentText()
        if pixel_str and 'x' in pixel_str:
            w, h = parse_pixel_string(pixel_str)
        # 其次按常见比例
        elif self.ratio_combo.currentText() and self.ratio_combo.currentText() != "自定义":
            ratio_key = self.ratio_combo.currentText()
            ratio = COMMON_RATIOS[ratio_key]
            try:
                h = int(self.height_input.text()) if self.height_input.text() else 600
                w = int(h * ratio[0] / ratio[1])
            except Exception:
                QMessageBox.warning(self, "错误", "请输入有效的高度或选择常见像素")
                return
        # 最后按自定义像素
        else:
            try:
                w = int(self.width_input.text())
                h = int(self.height_input.text())
            except Exception:
                QMessageBox.warning(self, "错误", "请输入有效的宽度和高度")
                return

        ok, msg = set_window_size(window_title, w, h)
        if ok:
            QMessageBox.information(self, "成功", msg)
        else:
            QMessageBox.warning(self, "错误", msg)

    def on_reset_clicked(self):
        self.pixel_combo.setCurrentIndex(0)
        self.width_input.clear()
        self.height_input.clear()
        self.ratio_combo.setCurrentIndex(0)

if __name__ == "__main__":
    app = QApplication(sys.argv)
    resizer = WindowResizer()
    resizer.show()
    sys.exit(app.exec_())
