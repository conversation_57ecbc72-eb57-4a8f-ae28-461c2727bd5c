#!/usr/bin/env python3
"""
测试所有启动器的有效性
"""

import os
import subprocess
import time
import sys

def test_file_exists(filepath, description):
    """测试文件是否存在"""
    if os.path.exists(filepath):
        print(f"✅ {description}: {filepath}")
        return True
    else:
        print(f"❌ {description}: {filepath} (不存在)")
        return False

def test_launcher(command, description, timeout=3):
    """测试启动器是否能正常启动"""
    print(f"\n🧪 测试 {description}...")
    try:
        # 启动进程
        process = subprocess.Popen(
            command,
            shell=True,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            cwd="setWindow"
        )
        
        # 等待一段时间看是否正常启动
        time.sleep(timeout)
        
        # 检查进程状态
        if process.poll() is None:
            print(f"✅ {description} 启动成功")
            # 终止进程
            process.terminate()
            try:
                process.wait(timeout=2)
            except subprocess.TimeoutExpired:
                process.kill()
            return True
        else:
            stdout, stderr = process.communicate()
            if stderr:
                print(f"❌ {description} 启动失败: {stderr.decode()}")
            else:
                print(f"❌ {description} 意外退出")
            return False
            
    except Exception as e:
        print(f"❌ {description} 测试异常: {e}")
        return False

def main():
    print("🔧 启动器测试工具")
    print("=" * 50)
    
    # 检查文件存在性
    print("📁 检查文件存在性:")
    files_to_check = [
        ("setWindow/w3.py", "主程序"),
        ("setWindow/run_w3.bat", "详细BAT启动器"),
        ("setWindow/w3_simple.bat", "简洁BAT启动器"),
        ("setWindow/run.vbs", "完整VBS启动器"),
        ("setWindow/run_simple.vbs", "简洁VBS启动器")
    ]
    
    all_files_exist = True
    for filepath, desc in files_to_check:
        if not test_file_exists(filepath, desc):
            all_files_exist = False
    
    if not all_files_exist:
        print("\n❌ 部分文件缺失，无法进行启动测试")
        return False
    
    print("\n" + "=" * 50)
    print("🚀 启动器功能测试:")
    
    # 测试各种启动器
    launchers = [
        ("python setWindow/w3.py", "直接Python启动"),
        ("setWindow/run_w3.bat", "详细BAT启动器"),
        ("setWindow/w3_simple.bat", "简洁BAT启动器"),
        ("cscript setWindow/run.vbs", "完整VBS启动器"),
        ("cscript setWindow/run_simple.vbs", "简洁VBS启动器")
    ]
    
    results = []
    for command, description in launchers:
        success = test_launcher(command, description)
        results.append((description, success))
    
    # 总结结果
    print("\n" + "=" * 50)
    print("📊 测试结果总结:")
    
    passed = 0
    total = len(results)
    
    for desc, success in results:
        status = "✅ 通过" if success else "❌ 失败"
        print(f"  {desc}: {status}")
        if success:
            passed += 1
    
    print(f"\n📈 总体结果: {passed}/{total} 个启动器测试通过")
    
    if passed == total:
        print("🎉 所有启动器都工作正常！")
        print("\n💡 推荐使用:")
        print("  • 日常使用: run_simple.vbs (静默启动)")
        print("  • 调试诊断: run_w3.bat (详细信息)")
        print("  • 快速启动: w3_simple.bat (最小输出)")
    else:
        print("⚠️ 部分启动器存在问题，请检查:")
        print("  • Python是否正确安装并在PATH中")
        print("  • PyQt5和pygetwindow模块是否已安装")
        print("  • Windows Script Host是否启用")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    input("\n按回车键退出...")
    sys.exit(0 if success else 1)
