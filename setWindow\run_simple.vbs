' ============================================================================
' Simple Window Management Tool VBS Launcher
' ============================================================================
' Author: Liu Lifu
' Version: 2025-01-11-15:45:00
' Purpose: Minimal VBS launcher for w3.py (silent execution)
' ============================================================================

' Create objects
Set ws = CreateObject("WScript.Shell")
Set fso = CreateObject("Scripting.FileSystemObject")

' Get script directory and build path to w3.py
scriptDir = fso.GetParentFolderName(WScript.ScriptFullName)
w3Path = scriptDir & "\w3.py"

' Run Python script silently (0 = hidden window)
ws.Run "python """ & w3Path & """", 0

' Clean up
Set ws = Nothing
Set fso = Nothing
