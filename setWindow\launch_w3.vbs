' ============================================================================
' Window Management Tool - Invisible Launcher
' ============================================================================
' Author: Liu Lifu  
' Version: 2025-01-11-16:15:00
' Purpose: Launch w3.py with absolutely no visible windows
' Usage: Double-click this file or create shortcut to it
' ============================================================================

On Error Resume Next

' Create required objects
Set ws = CreateObject("WScript.Shell")
Set fso = CreateObject("Scripting.FileSystemObject")

' Get paths
scriptDir = fso.GetParentFolderName(WScript.ScriptFullName)
w3Path = scriptDir & "\w3.py"

' Validate w3.py exists
If Not fso.FileExists(w3Path) Then
    MsgBox "Window Management Tool Error:" & vbCrLf & vbCrLf & _
           "w3.py not found in:" & vbCrLf & scriptDir & vbCrLf & vbCrLf & _
           "Please ensure w3.py is in the same folder as this launcher.", _
           vbCritical + vbSystemModal, "Launch Error"
    WScript.Quit 1
End If

' Try multiple Python launch methods
Dim commands(2)
commands(0) = "python """ & w3Path & """"
commands(1) = "py """ & w3Path & """"
commands(2) = "python.exe """ & w3Path & """"

Dim launched
launched = False

For i = 0 To UBound(commands)
    If Not launched Then
        Err.Clear
        ' Launch with: WindowStyle=0 (hidden), WaitOnReturn=False (don't wait)
        ws.Run commands(i), 0, False
        
        If Err.Number = 0 Then
            launched = True
        End If
    End If
Next

' Show error if all methods failed
If Not launched Then
    MsgBox "Window Management Tool Error:" & vbCrLf & vbCrLf & _
           "Could not launch Python script." & vbCrLf & vbCrLf & _
           "Please ensure:" & vbCrLf & _
           "• Python 3.x is installed" & vbCrLf & _
           "• Python is in your system PATH" & vbCrLf & _
           "• PyQt5 and pygetwindow are installed" & vbCrLf & vbCrLf & _
           "Install missing modules with:" & vbCrLf & _
           "pip install PyQt5 pygetwindow", _
           vbCritical + vbSystemModal, "Launch Error"
End If

' Cleanup
Set ws = Nothing
Set fso = Nothing
